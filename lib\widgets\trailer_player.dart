import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'package:google_fonts/google_fonts.dart';

class TrailerPlayer extends StatefulWidget {
  final String? trailerUrl;
  final String movieTitle;

  const TrailerPlayer({
    Key? key,
    required this.trailerUrl,
    required this.movieTitle,
  }) : super(key: key);

  @override
  State<TrailerPlayer> createState() => _TrailerPlayerState();
}

class _TrailerPlayerState extends State<TrailerPlayer> {
  VideoPlayerController? _videoController;
  YoutubePlayerController? _youtubeController;
  bool _isYouTubeVideo = false;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Không có trailer cho phim này';
      });
      return;
    }

    // Check if it's a YouTube URL
    final youtubeVideoId = YoutubePlayer.convertUrlToId(widget.trailerUrl!);

    if (youtubeVideoId != null) {
      _initializeYouTubePlayer(youtubeVideoId);
    } else {
      _initializeVideoPlayer();
    }
  }

  void _initializeYouTubePlayer(String videoId) {
    try {
      _youtubeController = YoutubePlayerController(
        initialVideoId: videoId,
        flags: const YoutubePlayerFlags(
          autoPlay: false,
          mute: false,
          enableCaption: true,
          captionLanguage: 'vi',
        ),
      );

      setState(() {
        _isYouTubeVideo = true;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Lỗi khi tải trailer: $e';
      });
    }
  }

  void _initializeVideoPlayer() {
    try {
      _videoController = VideoPlayerController.networkUrl(
        Uri.parse(widget.trailerUrl!),
        videoPlayerOptions: VideoPlayerOptions(
          mixWithOthers: true,
          allowBackgroundPlayback: false,
        ),
      );

      _videoController!.initialize().then((_) {
        setState(() {
          _isLoading = false;
        });
      }).catchError((error) {
        print('Video player initialization error: $error');
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = 'Định dạng video không được hỗ trợ hoặc lỗi kết nối';
        });
      });
    } catch (e) {
      print('Video player setup error: $e');
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'URL trailer không hợp lệ';
      });
    }
  }

  @override
  void dispose() {
    _videoController?.dispose();
    _youtubeController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trailer',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.black,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: _buildPlayerContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlayerContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white54,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Không thể tải trailer',
              style: GoogleFonts.mulish(
                color: Colors.white54,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_isYouTubeVideo && _youtubeController != null) {
      return YoutubePlayer(
        controller: _youtubeController!,
        showVideoProgressIndicator: true,
        progressIndicatorColor: Colors.red,
        progressColors: const ProgressBarColors(
          playedColor: Colors.red,
          handleColor: Colors.redAccent,
        ),
      );
    }

    if (_videoController != null && _videoController!.value.isInitialized) {
      return Stack(
        alignment: Alignment.center,
        children: [
          AspectRatio(
            aspectRatio: _videoController!.value.aspectRatio,
            child: VideoPlayer(_videoController!),
          ),
          _buildVideoControls(),
        ],
      );
    }

    return Center(
      child: Text(
        'Không thể phát trailer',
        style: GoogleFonts.mulish(
          color: Colors.white54,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildVideoControls() {
    return Container(
      color: Colors.black26,
      child: Center(
        child: IconButton(
          onPressed: () {
            setState(() {
              if (_videoController!.value.isPlaying) {
                _videoController!.pause();
              } else {
                _videoController!.play();
              }
            });
          },
          icon: Icon(
            _videoController!.value.isPlaying
                ? Icons.pause_circle_filled
                : Icons.play_circle_filled,
            size: 64,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
