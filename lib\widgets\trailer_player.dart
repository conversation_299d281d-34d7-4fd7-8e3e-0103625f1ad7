import 'package:flutter/material.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:google_fonts/google_fonts.dart';

class TrailerPlayer extends StatefulWidget {
  final String? trailerUrl;
  final String movieTitle;

  const TrailerPlayer({
    Key? key,
    required this.trailerUrl,
    required this.movieTitle,
  }) : super(key: key);

  @override
  State<TrailerPlayer> createState() => _TrailerPlayerState();
}

class _TrailerPlayerState extends State<TrailerPlayer> {
  late final Player _player;
  late final VideoController _controller;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  @override
  void didUpdateWidget(TrailerPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if trailer URL changed
    if (oldWidget.trailerUrl != widget.trailerUrl) {
      print(
          'Trailer URL changed from ${oldWidget.trailerUrl} to ${widget.trailerUrl}');
      _resetState();
      _initializePlayer();
    }
  }

  void _resetState() {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _errorMessage = null;
      });
    }
  }

  void _initializePlayer() {
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = 'Không có trailer cho phim này';
        });
      }
      return;
    }

    try {
      // Create player instance
      _player = Player();

      // Create video controller
      _controller = VideoController(_player);

      // Listen to player events
      _player.stream.error.listen((error) {
        if (mounted) {
          print('Media Kit Player Error: $error');
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = 'Không thể phát video: ${error.toString()}';
          });
        }
      });

      _player.stream.buffering.listen((buffering) {
        if (mounted && !_hasError) {
          setState(() {
            _isLoading = buffering;
          });
        }
      });

      // Open media source
      _player.open(Media(widget.trailerUrl!)).then((_) {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _hasError = false;
          });
        }
      }).catchError((error) {
        if (mounted) {
          print('Media Kit Open Error: $error');
          setState(() {
            _isLoading = false;
            _hasError = true;
            _errorMessage = 'Không thể tải video: ${error.toString()}';
          });
        }
      });
    } catch (e) {
      print('Media Kit Setup Error: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = 'Lỗi khởi tạo video player: $e';
        });
      }
    }
  }

  @override
  void dispose() {
    try {
      _player.dispose();
    } catch (e) {
      print('Error disposing player: $e');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Don't show anything if no trailer URL
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trailer',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.black,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: _buildPlayerContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlayerContent() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            const SizedBox(height: 8),
            Text(
              'Đang tải trailer...',
              style: GoogleFonts.mulish(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white54,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'Không thể phát trailer',
              style: GoogleFonts.mulish(
                color: Colors.white54,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () {
                _resetState();
                _initializePlayer();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.black,
              ),
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    // Show video player
    return Video(
      controller: _controller,
      controls: AdaptiveVideoControls,
    );
  }
}
