import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:better_player/better_player.dart';
import 'package:google_fonts/google_fonts.dart';

class TrailerPlayer extends StatefulWidget {
  final String? trailerUrl;
  final String movieTitle;

  const TrailerPlayer({
    Key? key,
    required this.trailerUrl,
    required this.movieTitle,
  }) : super(key: key);

  @override
  State<TrailerPlayer> createState() => _TrailerPlayerState();
}

class _TrailerPlayerState extends State<TrailerPlayer> {
  BetterPlayerController? _betterPlayerController;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  @override
  void didUpdateWidget(TrailerPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Check if trailer URL changed
    if (oldWidget.trailerUrl != widget.trailerUrl) {
      print('Trailer URL changed from ${oldWidget.trailerUrl} to ${widget.trailerUrl}');
      _disposeCurrentPlayer();
      _resetState();
      _initializePlayer();
    }
  }

  void _disposeCurrentPlayer() {
    _betterPlayerController?.dispose();
    _betterPlayerController = null;
  }

  void _resetState() {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _errorMessage = null;
      });
    }
  }

  void _initializePlayer() {
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _errorMessage = 'Không có trailer cho phim này';
        });
      }
      return;
    }

    _setupBetterPlayer();
  }

  void _setupBetterPlayer() {
    try {
      // Better Player Configuration
      BetterPlayerConfiguration betterPlayerConfiguration = BetterPlayerConfiguration(
        aspectRatio: 16 / 9,
        fit: BoxFit.contain,
        autoPlay: false,
        looping: false,
        fullScreenByDefault: false,
        allowedScreenSleep: false,
        deviceOrientationsAfterFullScreen: [
          DeviceOrientation.portraitUp,
          DeviceOrientation.portraitDown,
        ],
        systemOverlaysAfterFullScreen: [SystemUiOverlay.top, SystemUiOverlay.bottom],
        controlsConfiguration: BetterPlayerControlsConfiguration(
          enableProgressText: true,
          enableProgressBar: true,
          enablePlayPause: true,
          enableMute: true,
          enableFullscreen: true,
          enableRetry: true,
          enableAudioTracks: false,
          enableSubtitles: false,
          enableQualities: false,
          enablePlaybackSpeed: false,
          progressBarPlayedColor: Colors.red,
          progressBarHandleColor: Colors.redAccent,
          progressBarBufferedColor: Colors.grey[300]!,
          progressBarBackgroundColor: Colors.grey[600]!,
          controlBarColor: Colors.black54,
          iconsColor: Colors.white,
          textColor: Colors.white,
          loadingColor: Colors.white,
        ),
        errorBuilder: (context, errorMessage) {
          return _buildErrorWidget();
        },
      );

      // Data Source Configuration
      BetterPlayerDataSource dataSource;
      
      // Check if it's a YouTube URL
      if (_isYouTubeUrl(widget.trailerUrl!)) {
        // For YouTube URLs, we need to extract video ID and use YouTube format
        final videoId = _extractYouTubeVideoId(widget.trailerUrl!);
        if (videoId != null) {
          // Use YouTube URL format that Better Player can handle
          dataSource = BetterPlayerDataSource(
            BetterPlayerDataSourceType.network,
            'https://www.youtube.com/watch?v=$videoId',
            videoFormat: BetterPlayerVideoFormat.other,
            headers: {
              'User-Agent': 'Mozilla/5.0 (compatible; TrailerPlayer/1.0)',
            },
          );
        } else {
          _setError('URL YouTube không hợp lệ');
          return;
        }
      } else {
        // For direct video URLs
        dataSource = BetterPlayerDataSource(
          BetterPlayerDataSourceType.network,
          widget.trailerUrl!,
          videoFormat: BetterPlayerVideoFormat.other,
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; TrailerPlayer/1.0)',
          },
        );
      }

      // Create Better Player Controller
      _betterPlayerController = BetterPlayerController(
        betterPlayerConfiguration,
        betterPlayerDataSource: dataSource,
      );

      // Add event listener
      _betterPlayerController!.addEventsListener((event) {
        if (event.betterPlayerEventType == BetterPlayerEventType.initialized) {
          if (mounted) {
            setState(() {
              _isLoading = false;
              _hasError = false;
            });
          }
        } else if (event.betterPlayerEventType == BetterPlayerEventType.exception) {
          _setError('Lỗi khi phát video: ${event.parameters?['exception'] ?? 'Unknown error'}');
        }
      });

    } catch (e) {
      print('Better Player setup error: $e');
      _setError('Không thể khởi tạo video player: $e');
    }
  }

  bool _isYouTubeUrl(String url) {
    return url.contains('youtube.com') || url.contains('youtu.be');
  }

  String? _extractYouTubeVideoId(String url) {
    RegExp regExp = RegExp(
      r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
      caseSensitive: false,
    );
    Match? match = regExp.firstMatch(url);
    return match?.group(1);
  }

  void _setError(String message) {
    if (mounted) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = message;
      });
    }
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.white54,
            size: 48,
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? 'Không thể phát trailer',
            style: GoogleFonts.mulish(
              color: Colors.white54,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: () {
              _resetState();
              _initializePlayer();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
            ),
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _disposeCurrentPlayer();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Don't show anything if no trailer URL
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trailer',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.black,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: _buildPlayerContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlayerContent() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            const SizedBox(height: 8),
            Text(
              'Đang tải trailer...',
              style: GoogleFonts.mulish(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return _buildErrorWidget();
    }

    if (_betterPlayerController != null) {
      return BetterPlayer(controller: _betterPlayerController!);
    }

    return Center(
      child: Text(
        'Không thể phát trailer',
        style: GoogleFonts.mulish(
          color: Colors.white54,
          fontSize: 14,
        ),
      ),
    );
  }
}
