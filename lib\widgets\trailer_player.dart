import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_fonts/google_fonts.dart';

class TrailerPlayer extends StatefulWidget {
  final String? trailerUrl;
  final String movieTitle;

  const TrailerPlayer({
    Key? key,
    required this.trailerUrl,
    required this.movieTitle,
  }) : super(key: key);

  @override
  State<TrailerPlayer> createState() => _TrailerPlayerState();
}

class _TrailerPlayerState extends State<TrailerPlayer> {
  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkTrailerUrl();
  }

  void _checkTrailerUrl() {
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      _setError('Không có trailer cho phim này');
    }
  }

  Future<void> _launchTrailer() async {
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      _setError('Không có URL trailer');
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final Uri url = Uri.parse(widget.trailerUrl!);

      if (await canLaunchUrl(url)) {
        await launchUrl(
          url,
          mode: LaunchMode.externalApplication,
        );
        setState(() {
          _isLoading = false;
        });
      } else {
        _setError('Không thể mở trailer');
      }
    } catch (e) {
      _setError('Lỗi khi mở trailer: $e');
    }
  }

  void _setError(String message) {
    if (mounted) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = message;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Don't show anything if no trailer URL
    if (widget.trailerUrl == null || widget.trailerUrl!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trailer',
            style: GoogleFonts.mulish(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.black,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: _buildPlayerContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlayerContent() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            const SizedBox(height: 8),
            Text(
              'Đang tải trailer...',
              style: GoogleFonts.mulish(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white54,
              size: 32,
            ),
            const SizedBox(height: 4),
            Flexible(
              child: Text(
                _errorMessage ?? 'Không thể phát trailer',
                style: GoogleFonts.mulish(
                  color: Colors.white54,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 4),
            ElevatedButton(
              onPressed: () {
                _launchTrailer();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.black,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                minimumSize: const Size(0, 32),
              ),
              child: const Text('Thử lại', style: TextStyle(fontSize: 12)),
            ),
          ],
        ),
      );
    }

    // Show trailer button
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.play_circle_outline,
            size: 64,
            color: Colors.white.withOpacity(0.8),
          ),
          const SizedBox(height: 12),
          Text(
            'Xem Trailer',
            style: GoogleFonts.mulish(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.movieTitle,
            style: GoogleFonts.mulish(
              color: Colors.white70,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _launchTrailer,
            icon: const Icon(Icons.play_arrow, size: 20),
            label: const Text('Phát Trailer'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
        ],
      ),
    );
  }
}
