# Test Trailer Functionality

## Chức năng đã thêm:

### 1. TrailerPlayer Widget
- **Vị trí**: `lib/widgets/trailer_player.dart`
- **Chức năng**: 
  - Hỗ trợ phát video từ URL trực tiếp
  - Hỗ trợ phát video YouTube
  - <PERSON><PERSON> controls play/pause cho video thường
  - Tự động detect YouTube URLs
  - Error handling khi không thể tải video

### 2. Tích hợp vào MovieDetailsPage
- **Vị trí**: `lib/view/page/movie_detail_page.dart`
- **Thay đổi**:
  - Thêm import TrailerPlayer widget
  - Thêm TrailerPlayer vào phần nội dung phim (sau overview, trước cast)
  - Chỉ hiển thị khi movie có trailerUrl
  - Áp dụng cho cả phim từ database và legacy movie

### 3. Dependencies đã thêm
- **video_player**: ^2.8.2 - <PERSON><PERSON> phát video thường
- **youtube_player_flutter**: ^8.1.2 - <PERSON><PERSON> phá<PERSON> video YouTube

## Cách test:

### Test với phim từ database:
1. Mở ứng dụng
2. Vào trang chi tiết phim có trailerUrl
3. Scroll xuống phần trailer
4. Nhấn play để xem trailer

### Test với legacy movie (Thor):
1. Mở ứng dụng khi không có movieId
2. Sẽ hiển thị legacy movie Thor: The Dark World
3. Scroll xuống sẽ thấy phần trailer YouTube
4. Nhấn play để xem trailer

## Trailer URLs để test:
- **YouTube**: https://www.youtube.com/watch?v=npvJ9FTgZbM (Thor: The Dark World)
- **Direct video**: Có thể thêm URL video .mp4 trực tiếp

## Lưu ý:
- Trailer chỉ hiển thị khi có trailerUrl
- YouTube videos sẽ sử dụng YouTube player
- Video thường sẽ sử dụng video player với controls
- Có error handling khi không thể tải video
- UI responsive và phù hợp với theme của app
